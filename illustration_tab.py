import streamlit as st
import pandas as pd
from datetime import datetime

def illustration_page():
    # Enhanced CSS styling for illustration page
    st.markdown("""
    <style>
        /* Hide Streamlit elements */
        #MainMenu {visibility: hidden;}
        footer {visibility: hidden;}
        .stDeployButton {display: none;}
        header {visibility: hidden;}

        /* Page background */
        .stApp {
            background-color: #f8f9fa;
        }

        /* Main title */
        .main-title {
            text-align: center;
            font-size: 3rem;
            font-weight: 700;
            color: #1a252f;
            margin: 25px 0;
            padding: 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white !important;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 1px;
        }

        /* Tab styling */
        .stTabs [data-baseweb="tab-list"] {
            gap: 8px;
            background-color: #f1f3f4;
            border-radius: 10px;
            padding: 5px;
        }

        .stTabs [data-baseweb="tab"] {
            height: 50px;
            padding: 0px 24px;
            background-color: transparent;
            border-radius: 8px;
            color: #5f6368;
            font-weight: 500;
            font-size: 16px;
            border: none;
        }

        .stTabs [aria-selected="true"] {
            background-color: #3498db !important;
            color: white !important;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }

        /* Info cards */
        .info-card {
            background: white;
            padding: 25px;
            margin: 15px 0;
            border-radius: 12px;
            box-shadow: 0 3px 12px rgba(0,0,0,0.1);
            border-left: 5px solid #3498db;
            transition: transform 0.2s ease;
        }

        .info-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .info-card h3 {
            color: #2c3e50;
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .info-card p {
            color: #34495e;
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        /* Feature list */
        .feature-list {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }

        .feature-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            color: #2c3e50;
            font-weight: 500;
        }

        .feature-icon {
            color: #27ae60;
            font-size: 1.2rem;
            margin-right: 12px;
        }

        /* What-If specific styling */
        .what-if-container {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            padding: 20px;
            border-radius: 12px;
            margin: 15px 0;
            border-left: 5px solid #28a745;
        }

        .tool-card {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .tool-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
            border-color: #3498db;
        }
    </style>
    """, unsafe_allow_html=True)

    # Check if we're in a What-If sub-page
    current_what_if = st.session_state.get('current_what_if_page')

    if current_what_if:
        # Show What-If specific content based on selected tool
        show_what_if_content(current_what_if)
    else:
        # Show regular illustration page with tabs
        # Main Title
        st.markdown("<h1 class='main-title'>📊 Policy Illustration System</h1>", unsafe_allow_html=True)

        # Create tabs for the illustration system
        tab1, tab2 = st.tabs(["📋 Information & Overview", "⚙️ Configure Scenarios"])

        with tab1:
            illustration_info_tab()

        with tab2:
            illustration_scenario_tab()

def show_what_if_content(what_if_page):
    """Display content for What-If Illustration sub-pages"""

    if what_if_page == "scenario_builder":
        show_scenario_builder()
    elif what_if_page == "comparison_tool":
        show_comparison_tool()
    elif what_if_page == "report_generator":
        show_report_generator()
    elif what_if_page == "advanced_settings":
        show_advanced_settings()

def show_scenario_builder():
    """Scenario Builder What-If Tool"""
    st.markdown("<h1 class='main-title'>📈 Scenario Builder</h1>", unsafe_allow_html=True)

    st.markdown("""
    <div class='what-if-container'>
        <h3>🎯 Build Custom Scenarios</h3>
        <p>Create and customize detailed policy scenarios with advanced parameters and projections.</p>
    </div>
    """, unsafe_allow_html=True)

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        <div class='tool-card'>
            <h4>📊 Scenario Parameters</h4>
        </div>
        """, unsafe_allow_html=True)

        scenario_name = st.text_input("Scenario Name", value="Custom Scenario 1")
        scenario_type = st.selectbox("Scenario Type", ["Conservative", "Moderate", "Aggressive", "Custom"])
        projection_years = st.slider("Projection Years", 5, 50, 20)

    with col2:
        st.markdown("""
        <div class='tool-card'>
            <h4>💰 Financial Assumptions</h4>
        </div>
        """, unsafe_allow_html=True)

        growth_rate = st.slider("Expected Growth Rate (%)", 1.0, 15.0, 6.5, 0.1)
        inflation_rate = st.slider("Inflation Rate (%)", 1.0, 8.0, 3.0, 0.1)
        tax_rate = st.slider("Tax Rate (%)", 10.0, 50.0, 25.0, 1.0)

    if st.button("🚀 Build Scenario", type="primary", use_container_width=True):
        st.success(f"✅ Scenario '{scenario_name}' built successfully!")
        st.balloons()

def show_comparison_tool():
    """Comparison Tool What-If Tool"""
    st.markdown("<h1 class='main-title'>📊 Comparison Tool</h1>", unsafe_allow_html=True)

    st.markdown("""
    <div class='what-if-container'>
        <h3>⚖️ Compare Multiple Scenarios</h3>
        <p>Side-by-side comparison of different policy scenarios and their projected outcomes.</p>
    </div>
    """, unsafe_allow_html=True)

    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("""
        <div class='tool-card'>
            <h4>📈 Scenario A</h4>
        </div>
        """, unsafe_allow_html=True)
        st.selectbox("Select Scenario A", ["Base Policy", "Increased Coverage", "Premium Reduction"], key="scenario_a")

    with col2:
        st.markdown("""
        <div class='tool-card'>
            <h4>📊 Scenario B</h4>
        </div>
        """, unsafe_allow_html=True)
        st.selectbox("Select Scenario B", ["Base Policy", "Increased Coverage", "Premium Reduction"], key="scenario_b")

    with col3:
        st.markdown("""
        <div class='tool-card'>
            <h4>📋 Scenario C</h4>
        </div>
        """, unsafe_allow_html=True)
        st.selectbox("Select Scenario C", ["Base Policy", "Increased Coverage", "Premium Reduction"], key="scenario_c")

    if st.button("🔍 Compare Scenarios", type="primary", use_container_width=True):
        st.success("✅ Scenario comparison generated!")
        st.info("📊 Comparison results would be displayed here with charts and tables.")

def show_report_generator():
    """Report Generator What-If Tool"""
    st.markdown("<h1 class='main-title'>📋 Report Generator</h1>", unsafe_allow_html=True)

    st.markdown("""
    <div class='what-if-container'>
        <h3>📄 Generate Professional Reports</h3>
        <p>Create comprehensive reports with charts, tables, and detailed analysis.</p>
    </div>
    """, unsafe_allow_html=True)

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        <div class='tool-card'>
            <h4>📊 Report Configuration</h4>
        </div>
        """, unsafe_allow_html=True)

        report_type = st.selectbox("Report Type", ["Executive Summary", "Detailed Analysis", "Comparison Report", "Custom Report"])
        include_charts = st.checkbox("Include Charts", value=True)
        include_tables = st.checkbox("Include Data Tables", value=True)
        include_assumptions = st.checkbox("Include Assumptions", value=True)

    with col2:
        st.markdown("""
        <div class='tool-card'>
            <h4>🎨 Report Format</h4>
        </div>
        """, unsafe_allow_html=True)

        output_format = st.selectbox("Output Format", ["PDF", "Excel", "PowerPoint", "Word"])
        template = st.selectbox("Template", ["Professional", "Executive", "Technical", "Custom"])
        branding = st.checkbox("Include Company Branding", value=True)

    if st.button("📄 Generate Report", type="primary", use_container_width=True):
        st.success(f"✅ {report_type} generated in {output_format} format!")
        st.download_button("📥 Download Report", data="Sample report content", file_name=f"report.{output_format.lower()}")

def show_advanced_settings():
    """Advanced Settings What-If Tool"""
    st.markdown("<h1 class='main-title'>⚙️ Advanced Settings</h1>", unsafe_allow_html=True)

    st.markdown("""
    <div class='what-if-container'>
        <h3>🔧 Advanced Configuration</h3>
        <p>Fine-tune calculation methods, assumptions, and advanced parameters.</p>
    </div>
    """, unsafe_allow_html=True)

    tab1, tab2, tab3 = st.tabs(["🧮 Calculation Methods", "📊 Market Assumptions", "🔒 Risk Parameters"])

    with tab1:
        st.markdown("""
        <div class='tool-card'>
            <h4>🧮 Calculation Settings</h4>
        </div>
        """, unsafe_allow_html=True)

        calculation_method = st.selectbox("Calculation Method", ["Monte Carlo", "Deterministic", "Stochastic", "Hybrid"])
        iterations = st.number_input("Monte Carlo Iterations", 1000, 100000, 10000, 1000)
        confidence_level = st.slider("Confidence Level (%)", 90, 99, 95)

    with tab2:
        st.markdown("""
        <div class='tool-card'>
            <h4>📊 Market Assumptions</h4>
        </div>
        """, unsafe_allow_html=True)

        market_volatility = st.slider("Market Volatility (%)", 5.0, 30.0, 15.0, 0.5)
        correlation_factor = st.slider("Asset Correlation", -1.0, 1.0, 0.3, 0.1)
        economic_scenario = st.selectbox("Economic Scenario", ["Base Case", "Recession", "High Growth", "Stagflation"])

    with tab3:
        st.markdown("""
        <div class='tool-card'>
            <h4>🔒 Risk Parameters</h4>
        </div>
        """, unsafe_allow_html=True)

        mortality_adjustment = st.slider("Mortality Rate Adjustment (%)", -20, 20, 0)
        lapse_rate = st.slider("Expected Lapse Rate (%)", 0.0, 15.0, 3.0, 0.1)
        expense_ratio = st.slider("Expense Ratio (%)", 0.5, 5.0, 1.5, 0.1)

    if st.button("💾 Save Advanced Settings", type="primary", use_container_width=True):
        st.success("✅ Advanced settings saved successfully!")

    # Back to main illustration button
    if st.button("← Back to Main Illustration", key="back_to_main"):
        st.session_state.current_what_if_page = None
        st.rerun()

def illustration_info_tab():
    """Information and overview tab for policy illustration"""

    st.markdown("## Welcome to Policy Illustration System")
    st.markdown("---")

    # Overview section
    st.markdown("""
    <div class='info-card'>
        <h3>🎯 What is Policy Illustration?</h3>
        <p>Policy illustration is a comprehensive financial projection tool that helps you understand how your insurance policy will perform over time under various scenarios. It provides detailed insights into premium payments, cash values, death benefits, and policy performance projections.</p>
        <p>Our advanced illustration system allows you to model different financial scenarios and compare outcomes to make informed decisions about your insurance coverage.</p>
    </div>
    """, unsafe_allow_html=True)

    # Key features
    st.markdown("""
    <div class='info-card'>
        <h3>🚀 Key Features</h3>
        <div class='feature-list'>
            <div class='feature-item'>
                <span class='feature-icon'>✅</span>
                <span>As-Is Scenario Analysis - Current policy performance projections</span>
            </div>
            <div class='feature-item'>
                <span class='feature-icon'>✅</span>
                <span>What-If Scenario Modeling - Compare different coverage options</span>
            </div>
            <div class='feature-item'>
                <span class='feature-icon'>✅</span>
                <span>Financial Parameter Customization - Adjust income, premiums, and rates</span>
            </div>
            <div class='feature-item'>
                <span class='feature-icon'>✅</span>
                <span>Policy Lapse Analysis - Understand impact of policy termination</span>
            </div>
            <div class='feature-item'>
                <span class='feature-icon'>✅</span>
                <span>Comprehensive Reporting - Detailed scenario summaries and comparisons</span>
            </div>
            <div class='feature-item'>
                <span class='feature-icon'>✅</span>
                <span>Historical Tracking - Save and review previous illustration scenarios</span>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # How it works
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        <div class='info-card'>
            <h3>📈 How It Works</h3>
            <p><strong>Step 1:</strong> Select your base scenario (As-Is) representing your current policy situation.</p>
            <p><strong>Step 2:</strong> Choose What-If scenarios to explore different coverage options and financial strategies.</p>
            <p><strong>Step 3:</strong> Configure financial parameters including face amount, premiums, income, and interest rates.</p>
            <p><strong>Step 4:</strong> Generate comprehensive illustrations and compare scenarios side-by-side.</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class='info-card'>
            <h3>💡 Benefits</h3>
            <p><strong>Informed Decisions:</strong> Make data-driven choices about your insurance coverage.</p>
            <p><strong>Financial Planning:</strong> Understand long-term financial implications of policy decisions.</p>
            <p><strong>Risk Assessment:</strong> Evaluate different scenarios and their potential outcomes.</p>
            <p><strong>Optimization:</strong> Find the best balance between coverage and cost.</p>
        </div>
        """, unsafe_allow_html=True)

    # Getting started
    st.markdown("""
    <div class='info-card'>
        <h3>🎯 Getting Started</h3>
        <p>Ready to create your policy illustration? Click on the <strong>"Configure Scenarios"</strong> tab above to begin setting up your illustration parameters. You can create multiple scenarios and compare them to find the best option for your needs.</p>
        <p>If you have an existing policy from the Policy Selection module, those details will be automatically populated to streamline your illustration process.</p>
    </div>
    """, unsafe_allow_html=True)

    # Current policy info if available
    if st.session_state.get('current_policy'):
        policy = st.session_state.current_policy
        st.markdown("## 📋 Current Policy Information")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown(f"""
            <div class='info-card'>
                <h3>👤 Customer Details</h3>
                <p><strong>Name:</strong> {policy.get('customer_name', 'N/A')}</p>
                <p><strong>Policy Number:</strong> {policy.get('policy_number', 'N/A')}</p>
                <p><strong>Customer ID:</strong> {policy.get('customer_id', 'N/A')}</p>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            selected_policy = policy.get('selected_policy', {})
            st.markdown(f"""
            <div class='info-card'>
                <h3>🛡️ Policy Details</h3>
                <p><strong>Type:</strong> {selected_policy.get('name', 'N/A')}</p>
                <p><strong>Coverage:</strong> {selected_policy.get('coverage', 'N/A')}</p>
                <p><strong>Premium:</strong> {selected_policy.get('premium', 'N/A')}</p>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            st.markdown(f"""
            <div class='info-card'>
                <h3>📅 Policy Status</h3>
                <p><strong>Start Date:</strong> {policy.get('policy_start_date', 'N/A')}</p>
                <p><strong>Requirements:</strong> {len(policy.get('requirements', []))} items</p>
                <p><strong>Status:</strong> <span style='color: #27ae60; font-weight: 600;'>Active</span></p>
            </div>
            """, unsafe_allow_html=True)

def illustration_scenario_tab():
    """Scenario configuration tab with existing functionality"""

    st.markdown("## Configure Illustration Scenarios")
    st.markdown("Set up your policy illustration parameters and generate comprehensive projections.")
    st.markdown("---")

    col1, col2 = st.columns(2)
    with col1:
        st.subheader("🎯 Scenario Selection")
        as_is_option = st.selectbox(
            "As-Is (Direct Project)",
            ["Current Policy", "Base Scenario", "Standard Coverage"]
        )
        what_if_options = st.multiselect(
            "What-If Scenarios",
            ["Increased Coverage", "Premium Reduction", "Extended Term", "Early Retirement", "Economic Downturn"],
            default=["Increased Coverage"]
        )
        face_amount = st.number_input("Face Amount ($)", min_value=50000, max_value=2000000, value=250000, step=25000)
        premium = st.number_input("Annual Premium ($)", min_value=1000, max_value=50000, value=5000, step=500)

    with col2:
        st.subheader("💰 Financial Parameters")
        income_type = st.selectbox("Income Type", ["Salary", "Business", "Investment", "Loan", "Withdrawal"])
        annual_income = st.number_input("Annual Income ($)", min_value=30000, max_value=500000, value=75000, step=5000)
        loan_repayment = st.number_input("Loan Repayment ($)", min_value=0, max_value=10000, value=0, step=100)
        interest_rate = st.slider("Interest Rate (%)", min_value=1.0, max_value=15.0, value=5.5, step=0.1)
        lapse_year = st.slider("Policy Lapse Year (0 = No Lapse)", min_value=0, max_value=30, value=0)

    st.markdown("---")
    st.subheader("📊 Scenario Summary")
    scenario_data = {
        "As-Is": as_is_option,
        "What-If": ", ".join(what_if_options),
        "Face Amount": f"${face_amount:,}",
        "Premium": f"${premium:,}",
        "Income": f"${annual_income:,} ({income_type})",
        "Loan Repayment": f"${loan_repayment:,}",
        "Interest Rate": f"{interest_rate}%",
        "Lapse Year": lapse_year if lapse_year > 0 else "No Lapse"
    }
    scenario_df = pd.DataFrame(list(scenario_data.items()), columns=["Parameter", "Value"])
    st.dataframe(scenario_df, use_container_width=True)

    # Generate illustration button
    col1, col2 = st.columns([1, 1])
    with col2:
        if st.button("📈 Generate Illustration", use_container_width=True, type="primary"):
            new_scenario = {
                "id": len(st.session_state.scenarios_history) + 1,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "as_is": as_is_option,
                "what_if": what_if_options,
                "face_amount": face_amount,
                "premium": premium,
                "income": annual_income,
                "income_type": income_type,
                "loan_repayment": loan_repayment,
                "interest_rate": interest_rate,
                "lapse_year": lapse_year
            }
            st.session_state.scenarios_history.append(new_scenario)
            st.session_state.selected_scenarios.append(new_scenario)
            st.success("✅ Scenario illustration generated and added to history!")
            st.balloons()