import streamlit as st
import pandas as pd
from datetime import datetime

def illustration_page():
    # Enhanced CSS styling for illustration page
    st.markdown("""
    <style>
        /* Hide Streamlit elements */
        #MainMenu {visibility: hidden;}
        footer {visibility: hidden;}
        .stDeployButton {display: none;}
        header {visibility: hidden;}

        /* Page background */
        .stApp {
            background-color: #f8f9fa;
        }

        /* Main title */
        .main-title {
            text-align: center;
            font-size: 3rem;
            font-weight: 700;
            color: #1a252f;
            margin: 25px 0;
            padding: 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white !important;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 1px;
        }

        /* Tab styling */
        .stTabs [data-baseweb="tab-list"] {
            gap: 8px;
            background-color: #f1f3f4;
            border-radius: 10px;
            padding: 5px;
        }

        .stTabs [data-baseweb="tab"] {
            height: 50px;
            padding: 0px 24px;
            background-color: transparent;
            border-radius: 8px;
            color: #5f6368;
            font-weight: 500;
            font-size: 16px;
            border: none;
        }

        .stTabs [aria-selected="true"] {
            background-color: #3498db !important;
            color: white !important;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }

        /* Info cards */
        .info-card {
            background: white;
            padding: 25px;
            margin: 15px 0;
            border-radius: 12px;
            box-shadow: 0 3px 12px rgba(0,0,0,0.1);
            border-left: 5px solid #3498db;
            transition: transform 0.2s ease;
        }

        .info-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .info-card h3 {
            color: #2c3e50;
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .info-card p {
            color: #34495e;
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        /* Feature list */
        .feature-list {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }

        .feature-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            color: #2c3e50;
            font-weight: 500;
        }

        .feature-icon {
            color: #27ae60;
            font-size: 1.2rem;
            margin-right: 12px;
        }
    </style>
    """, unsafe_allow_html=True)

    # Main Title
    st.markdown("<h1 class='main-title'>📊 Policy Illustration System</h1>", unsafe_allow_html=True)

    # Create tabs for the illustration system
    tab1, tab2 = st.tabs(["📋 Information & Overview", "⚙️ Configure Scenarios"])

    with tab1:
        illustration_info_tab()

    with tab2:
        illustration_scenario_tab()

def illustration_info_tab():
    """Information and overview tab for policy illustration"""

    st.markdown("## Welcome to Policy Illustration System")
    st.markdown("---")

    # Overview section
    st.markdown("""
    <div class='info-card'>
        <h3>🎯 What is Policy Illustration?</h3>
        <p>Policy illustration is a comprehensive financial projection tool that helps you understand how your insurance policy will perform over time under various scenarios. It provides detailed insights into premium payments, cash values, death benefits, and policy performance projections.</p>
        <p>Our advanced illustration system allows you to model different financial scenarios and compare outcomes to make informed decisions about your insurance coverage.</p>
    </div>
    """, unsafe_allow_html=True)

    # Key features
    st.markdown("""
    <div class='info-card'>
        <h3>🚀 Key Features</h3>
        <div class='feature-list'>
            <div class='feature-item'>
                <span class='feature-icon'>✅</span>
                <span>As-Is Scenario Analysis - Current policy performance projections</span>
            </div>
            <div class='feature-item'>
                <span class='feature-icon'>✅</span>
                <span>What-If Scenario Modeling - Compare different coverage options</span>
            </div>
            <div class='feature-item'>
                <span class='feature-icon'>✅</span>
                <span>Financial Parameter Customization - Adjust income, premiums, and rates</span>
            </div>
            <div class='feature-item'>
                <span class='feature-icon'>✅</span>
                <span>Policy Lapse Analysis - Understand impact of policy termination</span>
            </div>
            <div class='feature-item'>
                <span class='feature-icon'>✅</span>
                <span>Comprehensive Reporting - Detailed scenario summaries and comparisons</span>
            </div>
            <div class='feature-item'>
                <span class='feature-icon'>✅</span>
                <span>Historical Tracking - Save and review previous illustration scenarios</span>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # How it works
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        <div class='info-card'>
            <h3>📈 How It Works</h3>
            <p><strong>Step 1:</strong> Select your base scenario (As-Is) representing your current policy situation.</p>
            <p><strong>Step 2:</strong> Choose What-If scenarios to explore different coverage options and financial strategies.</p>
            <p><strong>Step 3:</strong> Configure financial parameters including face amount, premiums, income, and interest rates.</p>
            <p><strong>Step 4:</strong> Generate comprehensive illustrations and compare scenarios side-by-side.</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class='info-card'>
            <h3>💡 Benefits</h3>
            <p><strong>Informed Decisions:</strong> Make data-driven choices about your insurance coverage.</p>
            <p><strong>Financial Planning:</strong> Understand long-term financial implications of policy decisions.</p>
            <p><strong>Risk Assessment:</strong> Evaluate different scenarios and their potential outcomes.</p>
            <p><strong>Optimization:</strong> Find the best balance between coverage and cost.</p>
        </div>
        """, unsafe_allow_html=True)

    # Getting started
    st.markdown("""
    <div class='info-card'>
        <h3>🎯 Getting Started</h3>
        <p>Ready to create your policy illustration? Click on the <strong>"Configure Scenarios"</strong> tab above to begin setting up your illustration parameters. You can create multiple scenarios and compare them to find the best option for your needs.</p>
        <p>If you have an existing policy from the Policy Selection module, those details will be automatically populated to streamline your illustration process.</p>
    </div>
    """, unsafe_allow_html=True)

    # Current policy info if available
    if st.session_state.get('current_policy'):
        policy = st.session_state.current_policy
        st.markdown("## 📋 Current Policy Information")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown(f"""
            <div class='info-card'>
                <h3>👤 Customer Details</h3>
                <p><strong>Name:</strong> {policy.get('customer_name', 'N/A')}</p>
                <p><strong>Policy Number:</strong> {policy.get('policy_number', 'N/A')}</p>
                <p><strong>Customer ID:</strong> {policy.get('customer_id', 'N/A')}</p>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            selected_policy = policy.get('selected_policy', {})
            st.markdown(f"""
            <div class='info-card'>
                <h3>🛡️ Policy Details</h3>
                <p><strong>Type:</strong> {selected_policy.get('name', 'N/A')}</p>
                <p><strong>Coverage:</strong> {selected_policy.get('coverage', 'N/A')}</p>
                <p><strong>Premium:</strong> {selected_policy.get('premium', 'N/A')}</p>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            st.markdown(f"""
            <div class='info-card'>
                <h3>📅 Policy Status</h3>
                <p><strong>Start Date:</strong> {policy.get('policy_start_date', 'N/A')}</p>
                <p><strong>Requirements:</strong> {len(policy.get('requirements', []))} items</p>
                <p><strong>Status:</strong> <span style='color: #27ae60; font-weight: 600;'>Active</span></p>
            </div>
            """, unsafe_allow_html=True)

def illustration_scenario_tab():
    """Scenario configuration tab with existing functionality"""

    st.markdown("## Configure Illustration Scenarios")
    st.markdown("Set up your policy illustration parameters and generate comprehensive projections.")
    st.markdown("---")

    col1, col2 = st.columns(2)
    with col1:
        st.subheader("🎯 Scenario Selection")
        as_is_option = st.selectbox(
            "As-Is (Direct Project)",
            ["Current Policy", "Base Scenario", "Standard Coverage"]
        )
        what_if_options = st.multiselect(
            "What-If Scenarios",
            ["Increased Coverage", "Premium Reduction", "Extended Term", "Early Retirement", "Economic Downturn"],
            default=["Increased Coverage"]
        )
        face_amount = st.number_input("Face Amount ($)", min_value=50000, max_value=2000000, value=250000, step=25000)
        premium = st.number_input("Annual Premium ($)", min_value=1000, max_value=50000, value=5000, step=500)

    with col2:
        st.subheader("💰 Financial Parameters")
        income_type = st.selectbox("Income Type", ["Salary", "Business", "Investment", "Loan", "Withdrawal"])
        annual_income = st.number_input("Annual Income ($)", min_value=30000, max_value=500000, value=75000, step=5000)
        loan_repayment = st.number_input("Loan Repayment ($)", min_value=0, max_value=10000, value=0, step=100)
        interest_rate = st.slider("Interest Rate (%)", min_value=1.0, max_value=15.0, value=5.5, step=0.1)
        lapse_year = st.slider("Policy Lapse Year (0 = No Lapse)", min_value=0, max_value=30, value=0)

    st.markdown("---")
    st.subheader("📊 Scenario Summary")
    scenario_data = {
        "As-Is": as_is_option,
        "What-If": ", ".join(what_if_options),
        "Face Amount": f"${face_amount:,}",
        "Premium": f"${premium:,}",
        "Income": f"${annual_income:,} ({income_type})",
        "Loan Repayment": f"${loan_repayment:,}",
        "Interest Rate": f"{interest_rate}%",
        "Lapse Year": lapse_year if lapse_year > 0 else "No Lapse"
    }
    scenario_df = pd.DataFrame(list(scenario_data.items()), columns=["Parameter", "Value"])
    st.dataframe(scenario_df, use_container_width=True)

    # Generate illustration button
    col1, col2 = st.columns([1, 1])
    with col2:
        if st.button("📈 Generate Illustration", use_container_width=True, type="primary"):
            new_scenario = {
                "id": len(st.session_state.scenarios_history) + 1,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "as_is": as_is_option,
                "what_if": what_if_options,
                "face_amount": face_amount,
                "premium": premium,
                "income": annual_income,
                "income_type": income_type,
                "loan_repayment": loan_repayment,
                "interest_rate": interest_rate,
                "lapse_year": lapse_year
            }
            st.session_state.scenarios_history.append(new_scenario)
            st.session_state.selected_scenarios.append(new_scenario)
            st.success("✅ Scenario illustration generated and added to history!")
            st.balloons()