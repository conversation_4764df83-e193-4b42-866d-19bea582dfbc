import streamlit as st
from datetime import datetime

# Enhanced customer data with multiple policies per customer (same as dashboard)
customer_data = {
    ("<PERSON>", "POL-12345678", "CUS-567890"): {
        "DOB": "05.02.1994",
        "Email": "<EMAIL>",
        "Phone": "(*************",
        "Address": "123 Main Street, New York, NY 10001",
        "Occupation": "Software Engineer",
        "Annual Income": "$85,000",
        "Customer ID": "CUS-567890",
        "Policy Number": "POL-12345678",
        "Policy Type": "Whole Life Insurance",
        "Status": "Active",
        "Available Policies": [
            {
                "id": "POL-12345678",
                "name": "Whole Life Insurance",
                "description": "Permanent life insurance with cash value accumulation",
                "coverage": "500,000 $",
                "premium": "2000 $ annually",
                "features": ["Cash Value Growth", "Tax Benefits", "Loan Options", "Guaranteed Death Benefit"]
            },
            {
                "id": "POL-12345679",
                "name": "Term Life Insurance",
                "description": "Affordable temporary life insurance coverage",
                "coverage": "750,000 $",
                "premium": "800 $ annually",
                "features": ["Lower Premiums", "Convertible Option", "Level Premiums", "Renewable"]
            },
            {
                "id": "POL-12345680",
                "name": "Universal Life Insurance",
                "description": "Flexible premium permanent life insurance",
                "coverage": "600,000 $",
                "premium": "1500 $ annually",
                "features": ["Flexible Premiums", "Investment Component", "Adjustable Death Benefit", "Tax Advantages"]
            },
            {
                "id": "POL-12345681",
                "name": "Disability Insurance",
                "description": "Income protection in case of disability",
                "coverage": "60% of income",
                "premium": "1200 $ annually",
                "features": ["Income Replacement", "Short & Long Term", "Partial Benefits", "Cost of Living Adjustments"]
            },
            {
                "id": "POL-12345682",
                "name": "Critical Illness Insurance",
                "description": "Lump sum payment for critical illness diagnosis",
                "coverage": "200,000 $",
                "premium": "600 $ annually",
                "features": ["Lump Sum Payment", "Multiple Conditions Covered", "No Restrictions on Use", "Return of Premium Option"]
            }
        ]
    },
    ("Jane Doe", "POL-87654321", "CUS-123456"): {
        "DOB": "12.08.1988",
        "Email": "<EMAIL>",
        "Phone": "(*************",
        "Address": "456 Oak Avenue, Los Angeles, CA 90210",
        "Occupation": "Marketing Manager",
        "Annual Income": "$75,000",
        "Customer ID": "CUS-123456",
        "Policy Number": "POL-87654321",
        "Policy Type": "Term Life Insurance",
        "Status": "Active",
        "Available Policies": [
            {
                "id": "POL-87654321",
                "name": "Term Life Insurance",
                "description": "Affordable temporary life insurance coverage",
                "coverage": "750,000 $",
                "premium": "150 $ monthly",
                "features": ["Affordable Premiums", "Convertible", "Level Death Benefit", "Renewable"]
            },
            {
                "id": "POL-87654322",
                "name": "Whole Life Insurance",
                "description": "Permanent life insurance with guaranteed cash value",
                "coverage": "500,000 $",
                "premium": "300 $ monthly",
                "features": ["Guaranteed Cash Value", "Dividends", "Loan Options", "Tax Benefits"]
            },
            {
                "id": "POL-87654323",
                "name": "Health Insurance",
                "description": "Comprehensive medical coverage",
                "coverage": "Unlimited",
                "premium": "450 $ monthly",
                "features": ["Preventive Care", "Prescription Coverage", "Specialist Access", "Emergency Care"]
            },
            {
                "id": "POL-87654324",
                "name": "Auto Insurance",
                "description": "Complete vehicle protection coverage",
                "coverage": "500,000 $ liability",
                "premium": "120 $ monthly",
                "features": ["Collision Coverage", "Comprehensive", "Liability Protection", "Roadside Assistance"]
            }
        ]
    },
    ("Michael Johnson", "POL-11111111", "CUS-111111"): {
        "DOB": "22.11.1985",
        "Email": "<EMAIL>",
        "Phone": "(*************",
        "Address": "789 Pine Street, Chicago, IL 60601",
        "Occupation": "Financial Advisor",
        "Annual Income": "$95,000",
        "Customer ID": "CUS-111111",
        "Policy Number": "POL-11111111",
        "Policy Type": "Investment Life Insurance",
        "Status": "Active",
        "Available Policies": [
            {
                "id": "POL-11111111",
                "name": "Investment Life Insurance",
                "description": "Life insurance with investment opportunities",
                "coverage": "800,000 $",
                "premium": "1500 $ quarterly",
                "features": ["Investment Growth", "Market Linked Returns", "Flexible Premiums", "Tax Efficiency"]
            },
            {
                "id": "POL-11111112",
                "name": "Retirement Annuity",
                "description": "Guaranteed income for retirement",
                "coverage": "Lifetime Income",
                "premium": "2000 $ quarterly",
                "features": ["Guaranteed Income", "Inflation Protection", "Death Benefits", "Tax Deferred Growth"]
            },
            {
                "id": "POL-11111113",
                "name": "Family Protection Plan",
                "description": "Comprehensive family coverage",
                "coverage": "1,000,000 $",
                "premium": "800 $ quarterly",
                "features": ["Family Coverage", "Child Benefits", "Spouse Protection", "Education Fund"]
            },
            {
                "id": "POL-11111114",
                "name": "Business Insurance",
                "description": "Key person and business protection",
                "coverage": "2,000,000 $",
                "premium": "3000 $ quarterly",
                "features": ["Key Person Coverage", "Business Loan Protection", "Buy-Sell Agreements", "Tax Benefits"]
            }
        ]
    }
}

def policy_selection_page():
    # Enhanced CSS styling for policy selection page matching the new image
    st.markdown("""
    <style>
        /* Hide Streamlit elements */
        #MainMenu {visibility: hidden;}
        footer {visibility: hidden;}
        .stDeployButton {display: none;}
        header {visibility: hidden;}

        /* Page background */
        .stApp {
            background-color: #f8f9fa;
        }

        /* Main title */
        .main-title {
            text-align: center;
            font-size: 3rem;
            font-weight: 700;
            color: #1a252f;
            margin: 25px 0;
            padding: 25px;
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            letter-spacing: 1px;
            border: 1px solid #e1e8ed;
        }

        /* Section headers */
        .section-header {
            font-size: 1.4rem;
            font-weight: 600;
            color: #1a252f;
            margin: 25px 0 15px 0;
            padding: 12px 0;
            border-bottom: 3px solid #3498db;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            letter-spacing: 0.5px;
        }

        /* Policy table styling */
        .policy-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 3px 12px rgba(0,0,0,0.15);
            margin: 20px 0;
            overflow: hidden;
            border: 1px solid #e1e8ed;
        }

        .policy-row {
            padding: 16px 20px;
            border-bottom: 1px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 15px;
            font-weight: 500;
        }

        .policy-row:hover {
            background-color: #f1f8ff;
            transform: translateX(5px);
            box-shadow: inset 5px 0 0 #3498db;
        }

        .policy-row.selected {
            background-color: #e8f4fd;
            border-left: 5px solid #3498db;
            font-weight: 600;
        }

        .status-active {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 13px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
            box-shadow: 0 2px 4px rgba(39, 174, 96, 0.3);
        }

        .status-pending {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 13px;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
            box-shadow: 0 2px 4px rgba(243, 156, 18, 0.3);
        }

        /* Detail cards */
        .detail-card {
            background: white;
            padding: 18px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.12);
            border-left: 4px solid #3498db;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .detail-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .detail-label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 15px;
            margin-bottom: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .detail-value {
            color: #1a252f;
            font-size: 17px;
            font-weight: 500;
            line-height: 1.4;
        }

        /* Button styling */
        .stButton > button {
            background: linear-gradient(135deg, #3498db, #2980b9) !important;
            color: white !important;
            border: none !important;
            padding: 12px 20px !important;
            border-radius: 8px !important;
            font-size: 15px !important;
            font-weight: 600 !important;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2) !important;
            box-shadow: 0 3px 6px rgba(52, 152, 219, 0.3) !important;
            transition: all 0.3s ease !important;
            letter-spacing: 0.5px !important;
        }

        .stButton > button:hover {
            background: linear-gradient(135deg, #2980b9, #1f4e79) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 5px 12px rgba(52, 152, 219, 0.4) !important;
        }

        /* Search section */
        .search-section {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.12);
            margin-bottom: 25px;
            border: 1px solid #e1e8ed;
        }

        /* Input field styling */
        .stTextInput > div > div > input {
            font-size: 15px !important;
            font-weight: 500 !important;
            padding: 12px 15px !important;
            border-radius: 8px !important;
            border: 2px solid #e1e8ed !important;
            transition: all 0.3s ease !important;
        }

        .stTextInput > div > div > input:focus {
            border-color: #3498db !important;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1) !important;
        }

        /* Multiselect styling */
        .stMultiSelect > div > div {
            font-size: 15px !important;
            font-weight: 500 !important;
        }

        /* Alert and info message styling */
        .stAlert {
            font-size: 15px !important;
            font-weight: 500 !important;
            border-radius: 8px !important;
            padding: 15px 20px !important;
        }

        .stInfo {
            background-color: #e8f4fd !important;
            border-left: 4px solid #3498db !important;
        }

        .stError {
            background-color: #fdf2f2 !important;
            border-left: 4px solid #e74c3c !important;
        }

        .stSuccess {
            background-color: #f0f9f4 !important;
            border-left: 4px solid #27ae60 !important;
        }

        .stWarning {
            background-color: #fef9e7 !important;
            border-left: 4px solid #f39c12 !important;
        }
    </style>
    """, unsafe_allow_html=True)

    # Main Title
    st.markdown("<h1 class='main-title'>Policy Search</h1>", unsafe_allow_html=True)

    # Policy Search Section
    st.markdown("<div class='search-section'>", unsafe_allow_html=True)
    st.markdown("<div class='section-header'>Policy Search</div>", unsafe_allow_html=True)

    col1, col2, col3, col4 = st.columns([2, 2, 2, 1])

    with col1:
        customer_name = st.text_input("Customer Name", placeholder="Enter customer name")

    with col2:
        policy_number = st.text_input("Policy Number", placeholder="Enter policy number")

    with col3:
        customer_id = st.text_input("Customer ID", placeholder="Enter customer ID")

    with col4:
        st.markdown("<br>", unsafe_allow_html=True)
        search_clicked = st.button("Search", type="primary", use_container_width=True)

    st.markdown("</div>", unsafe_allow_html=True)

    # Check if search was performed
    if search_clicked or (customer_name and policy_number and customer_id):
        # Create lookup key
        lookup_key = (customer_name.strip(), policy_number.strip(), customer_id.strip())

        # Check if customer exists in our data
        if lookup_key in customer_data:
            customer_details = customer_data[lookup_key]
            available_policies = customer_details.get("Available Policies", [])

            # Display search results
            st.markdown("<div class='section-header'>Search Results</div>", unsafe_allow_html=True)

            # Policy table header
            st.markdown("""
            <div class='policy-table'>
                <div style='background: linear-gradient(135deg, #34495e, #2c3e50); padding: 16px 20px; font-weight: 600; border-bottom: 3px solid #3498db; color: white;'>
                    <div style='display: grid; grid-template-columns: 2fr 1fr 1fr 1fr 1fr 0.5fr; gap: 20px; align-items: center; font-size: 16px; letter-spacing: 0.5px;'>
                        <div>Policy Number</div>
                        <div>Status</div>
                        <div>Type</div>
                        <div>Start Date</div>
                        <div>Face Amount</div>
                        <div>Action</div>
                    </div>
                </div>
            """, unsafe_allow_html=True)

            # Display available policies in table format
            for i, policy in enumerate(available_policies):
                status = "Active" if i < 2 else "Pending"
                status_class = "status-active" if status == "Active" else "status-pending"

                # Generate start date based on policy type
                start_date = "01/01/2024" if "Life" in policy['name'] else "15/03/2024"

                is_selected = st.session_state.get(f'selected_policy_index') == i
                row_class = "policy-row selected" if is_selected else "policy-row"

                st.markdown(f"""
                <div class='{row_class}'>
                    <div style='display: grid; grid-template-columns: 2fr 1fr 1fr 1fr 1fr 0.5fr; gap: 20px; align-items: center; font-size: 15px;'>
                        <div style='font-weight: 600; color: #2c3e50;'>{policy['id']}</div>
                        <div><span class='{status_class}'>{status}</span></div>
                        <div style='font-weight: 500; color: #34495e;'>{policy['name']}</div>
                        <div style='color: #7f8c8d;'>{start_date}</div>
                        <div style='font-weight: 600; color: #27ae60;'>{policy['coverage']}</div>
                        <div style='font-size: 18px;'>📋</div>
                    </div>
                </div>
                """, unsafe_allow_html=True)

                # Add selection button for each policy
                col1, col2, col3, col4, col6 = st.columns([2, 1, 1, 1, 0.5])
                with col6:
                    if st.button("Select", key=f"select_policy_{i}", help=f"Select {policy['name']}"):
                        st.session_state['selected_policy_index'] = i
                        st.session_state['selected_policy'] = policy
                        st.session_state['current_customer_data'] = {
                            'name': customer_name,
                            'policy_number': policy_number,
                            'customer_id': customer_id,
                            'details': customer_details
                        }
                        st.rerun()

            st.markdown("</div>", unsafe_allow_html=True)

        else:
            st.error("❌ Customer not found. Please check your details.")
            st.info("""
            **Sample Test Data:**
            - Customer Name: John Smith, Policy Number: POL-12345678, Customer ID: CUS-567890
            - Customer Name: Jane Doe, Policy Number: POL-87654321, Customer ID: CUS-123456
            - Customer Name: Michael Johnson, Policy Number: POL-11111111, Customer ID: CUS-111111
            """)

    else:
        st.info("ℹ️ Enter customer details and click Search to view available policies.")

    # Show selected policy details if a policy is selected
    if st.session_state.get('selected_policy'):
        selected_policy = st.session_state['selected_policy']
        current_customer_data = st.session_state.get('current_customer_data', {})

        st.markdown("<br>")
        st.markdown("<div class='section-header'>Life Insurance Plus</div>", unsafe_allow_html=True)

        # Policy details section matching the image layout
        col1, col2 = st.columns([1, 1])

        with col1:
            st.markdown("""
            <div class='section-header' style='font-size: 1.3rem; margin: 15px 0; color: #2c3e50;
                 background: linear-gradient(135deg, #ecf0f1, #bdc3c7); padding: 12px; border-radius: 8px;
                 text-align: center; font-weight: 600; box-shadow: 0 2px 6px rgba(0,0,0,0.1);'>
                👤 Customer Information
            </div>
            """, unsafe_allow_html=True)

            customer_info = [
                ("Full Name", current_customer_data.get('name', 'N/A')),
                ("Date of Birth", current_customer_data.get('details', {}).get('DOB', 'N/A')),
                ("Customer ID", current_customer_data.get('customer_id', 'N/A')),
                ("Email", current_customer_data.get('details', {}).get('Email', 'N/A')),
                ("Phone", current_customer_data.get('details', {}).get('Phone', 'N/A'))
            ]

            for label, value in customer_info:
                st.markdown(f"""
                <div class='detail-card'>
                    <div class='detail-label'>{label}</div>
                    <div class='detail-value'>{value}</div>
                </div>
                """, unsafe_allow_html=True)

            # Coverage Information
            st.markdown("""
            <div class='section-header' style='font-size: 1.3rem; margin: 25px 0 15px 0; color: #2c3e50;
                 background: linear-gradient(135deg, #e8f5e8, #c8e6c9); padding: 12px; border-radius: 8px;
                 text-align: center; font-weight: 600; box-shadow: 0 2px 6px rgba(0,0,0,0.1);'>
                🛡️ Coverage Information
            </div>
            """, unsafe_allow_html=True)

            coverage_info = [
                ("Policy Type", selected_policy['name']),
                ("Coverage", selected_policy['coverage']),
                ("Premium", selected_policy['premium']),
                ("Premium Type", "Level Premium"),
                ("Payment Frequency", "Monthly")
            ]

            for label, value in coverage_info:
                st.markdown(f"""
                <div class='detail-card'>
                    <div class='detail-label'>{label}</div>
                    <div class='detail-value'>{value}</div>
                </div>
                """, unsafe_allow_html=True)

        with col2:
            st.markdown("""
            <div class='section-header' style='font-size: 1.3rem; margin: 15px 0; color: #2c3e50;
                 background: linear-gradient(135deg, #fff3e0, #ffcc80); padding: 12px; border-radius: 8px;
                 text-align: center; font-weight: 600; box-shadow: 0 2px 6px rgba(0,0,0,0.1);'>
                📄 Policy Details
            </div>
            """, unsafe_allow_html=True)

            policy_details = [
                ("Policy Number", selected_policy['id']),
                ("Issue Date", "01/01/2024"),
                ("Status", "Active"),
                ("Type", selected_policy['name']),
                ("Premium", selected_policy['premium'])
            ]

            for label, value in policy_details:
                st.markdown(f"""
                <div class='detail-card'>
                    <div class='detail-label'>{label}</div>
                    <div class='detail-value'>{value}</div>
                </div>
                """, unsafe_allow_html=True)

            # Financial Details
            st.markdown("""
            <div class='section-header' style='font-size: 1.3rem; margin: 25px 0 15px 0; color: #2c3e50;
                 background: linear-gradient(135deg, #e8f5e8, #a5d6a7); padding: 12px; border-radius: 8px;
                 text-align: center; font-weight: 600; box-shadow: 0 2px 6px rgba(0,0,0,0.1);'>
                💰 Financial Details
            </div>
            """, unsafe_allow_html=True)

            financial_details = [
                ("Face Amount", selected_policy['coverage']),
                ("Cash Value", "$25,000"),
                ("Premium", selected_policy['premium']),
                ("Payment Frequency", "Monthly"),
                ("Next Due Date", "01/01/2024")
            ]

            for label, value in financial_details:
                st.markdown(f"""
                <div class='detail-card'>
                    <div class='detail-label'>{label}</div>
                    <div class='detail-value'>{value}</div>
                </div>
                """, unsafe_allow_html=True)

        # Policy Requirements Section
        st.markdown("<br>")
        st.markdown("<div class='section-header'>Policy Requirements</div>", unsafe_allow_html=True)

        # Policy requirements based on type
        requirements_options = {
            "Whole Life Insurance": ["Medical Exam Required", "Financial Statements", "Beneficiary Information", "Health Questionnaire"],
            "Term Life Insurance": ["Medical Exam Required", "Health Questionnaire", "Beneficiary Information"],
            "Universal Life Insurance": ["Medical Exam Required", "Financial Statements", "Investment Preferences", "Beneficiary Information"],
            "Disability Insurance": ["Medical Records Review", "Employment Verification", "Income Documentation", "Health Questionnaire"],
            "Critical Illness Insurance": ["Medical History Review", "Family Medical History", "Health Questionnaire", "Beneficiary Information"],
            "Investment Life Insurance": ["Financial Statements", "Investment Experience", "Risk Assessment", "Medical Exam Required"],
            "Retirement Annuity": ["Financial Planning Review", "Retirement Goals", "Income Documentation", "Beneficiary Information"],
            "Family Protection Plan": ["Family Information", "Medical Exams", "Financial Statements", "Beneficiary Details"],
            "Business Insurance": ["Business Financial Statements", "Key Person Information", "Business Valuation", "Partnership Agreements"],
            "Health Insurance": ["Medical History Review", "Current Health Status", "Preferred Providers", "Coverage Preferences"],
            "Auto Insurance": ["Driving Record Check", "Vehicle Information", "Coverage Preferences", "Previous Insurance History"]
        }

        policy_requirements = requirements_options.get(selected_policy['name'], ["General Requirements", "Documentation", "Application Form"])

        # Display requirements with enhanced styling
        st.markdown("""
        <div style='background: linear-gradient(135deg, #f8f9fa, #ffffff); padding: 20px; border-radius: 10px;
                    box-shadow: 0 3px 10px rgba(0,0,0,0.1); border: 1px solid #e1e8ed; margin: 15px 0;'>
            <h4 style='color: #2c3e50; font-weight: 600; margin-bottom: 15px; font-size: 18px;'>
                📋 Required Documents & Information
            </h4>
        </div>
        """, unsafe_allow_html=True)

        selected_requirements = st.multiselect(
            "Select Required Documents/Information:",
            options=policy_requirements,
            default=policy_requirements[:2],
            help="Choose the requirements that apply to your policy"
        )

        # Add some spacing before the button
        st.markdown("<br>")

        # Centered Get Illustration Button at the bottom
        col1, col2, col3 = st.columns([1, 1, 1])
        with col2:
            if st.button("🎯 Get Illustration", key="get_illustration", help="Generate policy illustration", type="primary", use_container_width=True):
                if selected_requirements:
                    # Store all policy information for illustration
                    st.session_state.current_policy = {
                        "customer_name": current_customer_data.get('name', 'N/A'),
                        "policy_number": current_customer_data.get('policy_number', 'N/A'),
                        "customer_id": current_customer_data.get('customer_id', 'N/A'),
                        "selected_policy": selected_policy,
                        "requirements": selected_requirements,
                        "policy_start_date": "01/01/2024"
                    }

                    # Navigate to illustration page
                    st.session_state.current_page = '📊 Illustration'
                    st.session_state.scroll_to_top = True
                    st.session_state.force_top_scroll = True
                    st.success("Generating policy illustration...")
                    st.rerun()
                else:
                    st.warning("Please select at least one requirement!")

    # Add some bottom spacing
    st.markdown("<br><br>", unsafe_allow_html=True)