import streamlit as st
from dashboard_tab import dashboard_page
from policy_selection_tab import policy_selection_page
from illustration_tab import illustration_page
from history_tab import history_page
from analysis_tab import analysis_page

# Page configuration
st.set_page_config(
    page_title="Insurance Policy Management System",
    page_icon="🏢",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'logged_in' not in st.session_state:
    st.session_state.logged_in = False
if 'scenarios_history' not in st.session_state:
    st.session_state.scenarios_history = []
if 'selected_scenarios' not in st.session_state:
    st.session_state.selected_scenarios = []

# Hardcoded credentials
USERS = {
    "admin": {"password": "admin123", "name": "Administrator"},
    "user1": {"password": "user123", "name": "<PERSON>"},
    "demo": {"password": "demo123", "name": "Demo User"}
}

def login_page():
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        st.markdown(
            """
            <h1 style='text-align: center; font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5em; color: #4F8BF9;'>Welcome to Insurance Portal</h1>
            <hr style='border: 1px solid #e6e6e6; margin-bottom: 2em;'>
            """,
            unsafe_allow_html=True
        )
        with st.form("login_form"):
            username = st.text_input("Username", placeholder="Enter your username")
            password = st.text_input("Password", type="password", placeholder="Enter your password")
            show_password = st.checkbox("Show Password")
            if show_password:
                password = st.text_input("Password (visible)", value=password, key="visible_password")
            col_login, col_reset, col_forgot = st.columns(3)
            with col_login:
                login_button = st.form_submit_button("Login", use_container_width=True)
            with col_reset:
                reset_button = st.form_submit_button("Reset", use_container_width=True)
            with col_forgot:
                forgot_button = st.form_submit_button("Forgot Password", use_container_width=True)
        if login_button:
            if username in USERS and USERS[username]["password"] == password:
                st.session_state.logged_in = True
                st.session_state.username = username
                st.session_state.user_name = USERS[username]["name"]
                st.success("Login successful!")
                st.rerun()
            else:
                st.error("Invalid username or password!")
        if reset_button:
            st.info("Form reset!")
            st.rerun()
        if forgot_button:
            st.info("Please contact administrator for password reset.")
        st.markdown("---")
        st.markdown("**Demo Credentials:**")
        st.code("Username: demo\nPassword: demo123")

def main():
    if not st.session_state.logged_in:
        login_page()
        return
    # Modern Sidebar Navigation with Enhanced Styling
    st.sidebar.markdown("""
    <style>
        /* Modern Sidebar Styling */
        .sidebar .sidebar-content {
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            padding: 0;
        }

        /* Navigation Header */
        .nav-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 20px;
            text-align: center;
            font-size: 1.4rem;
            font-weight: 700;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            border-radius: 0 0 15px 15px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        /* Welcome Message */
        .welcome-message {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #2c3e50;
            padding: 15px 20px;
            margin: 0 10px 25px 10px;
            border-radius: 12px;
            text-align: center;
            font-weight: 600;
            font-size: 1rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }

        /* All Navigation Buttons - Uniform Styling */
        .stButton > button, .dashboard-btn {
            background: transparent !important;
            color: #ecf0f1 !important;
            border: 1px solid transparent !important;
            padding: 14px 20px !important;
            margin: 3px 10px !important;
            border-radius: 10px !important;
            font-size: 1rem !important;
            font-weight: 500 !important;
            text-align: left !important;
            transition: all 0.3s ease !important;
            cursor: pointer !important;
            width: calc(100% - 20px) !important;
            height: 50px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: flex-start !important;
        }

        .stButton > button:hover, .dashboard-btn:hover {
            background: rgba(52, 152, 219, 0.2) !important;
            border-color: #3498db !important;
            transform: translateX(5px) !important;
            color: white !important;
        }

        /* Navigation Section Header */
        .nav-section-header {
            color: #7f8c8d;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 0 20px 10px 20px;
            margin-top: 10px;
            border-bottom: 1px solid #34495e;
        }

        /* Active Navigation Button */
        .nav-btn-active {
            background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
            color: white !important;
            border: 1px solid #e74c3c !important;
            box-shadow: 0 3px 10px rgba(231, 76, 60, 0.3) !important;
            font-weight: 600 !important;
            padding: 14px 20px !important;
            margin: 3px 10px !important;
            border-radius: 10px !important;
            font-size: 1rem !important;
            text-align: left !important;
            width: calc(100% - 20px) !important;
            height: 50px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: flex-start !important;
        }

        /* Logout Button - Special Styling by key */
        button[data-testid="baseButton-secondary"][title="Sign out of the application"] {
            background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
            color: white !important;
            border: 1px solid #e74c3c !important;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3) !important;
        }

        button[data-testid="baseButton-secondary"][title="Sign out of the application"]:hover {
            background: linear-gradient(135deg, #c0392b, #a93226) !important;
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4) !important;
        }
    </style>
    """, unsafe_allow_html=True)

    # Navigation Header
    st.sidebar.markdown("<div class='nav-header'>🏢 Insurance Portal</div>", unsafe_allow_html=True)

    # Welcome Message
    st.sidebar.markdown(f"<div class='welcome-message'>👋 Welcome, {st.session_state.get('user_name', '')}</div>", unsafe_allow_html=True)

    # Pages configuration
    pages = {
        "📊 Dashboard": dashboard_page,
        "📋 Policy Selection": policy_selection_page,
        "📊 Illustration": illustration_page,
        "📋 History": history_page,
        "📈 Analysis": analysis_page
    }

    if 'current_page' not in st.session_state:
        st.session_state.current_page = list(pages.keys())[0]

    # Dashboard Button (Special positioning below welcome)
    dashboard_key = "📊 Dashboard"
    if st.session_state.current_page == dashboard_key:
        st.sidebar.markdown(f"<div class='nav-btn-active'>📊 Dashboard ✓</div>", unsafe_allow_html=True)
    else:
        if st.sidebar.button("📊 Dashboard", key="dashboard_special", help="Go to Dashboard"):
            st.session_state.current_page = dashboard_key
            st.session_state.scroll_to_top = True
            st.rerun()

    # Navigation Section Header
    st.sidebar.markdown("<div class='nav-section-header'>Navigation Menu</div>", unsafe_allow_html=True)

    # Other Navigation Buttons
    other_pages = {k: v for k, v in pages.items() if k != dashboard_key}

    for page_name in other_pages.keys():
        if page_name == st.session_state.current_page:
            # Active state - using consistent styling
            icon = page_name.split()[0]  # Get the emoji
            name = ' '.join(page_name.split()[1:])  # Get the name without emoji
            st.sidebar.markdown(f"<div class='nav-btn-active'>{icon} {name} ✓</div>", unsafe_allow_html=True)
        else:
            # Regular button - all buttons will have same styling now
            if st.sidebar.button(page_name, key=f"nav_{page_name}"):
                st.session_state.current_page = page_name
                st.session_state.scroll_to_top = True
                st.rerun()

    # Spacer
    st.sidebar.markdown("<div style='height:30px'></div>", unsafe_allow_html=True)

    # Logout button with consistent styling
    if st.sidebar.button("🚪 Logout", key="logout_btn", help="Sign out of the application"):
        st.session_state.logged_in = False
        st.session_state.clear()
        st.rerun()
    # Handle scroll to top when navigating between pages
    if st.session_state.get('scroll_to_top', False) or st.session_state.get('force_top_scroll', False):
        st.markdown("""
        <script>
            function aggressiveScrollToTop() {
                // Multiple aggressive scroll attempts
                window.parent.document.body.scrollTop = 0;
                window.parent.document.documentElement.scrollTop = 0;
                window.parent.scrollTo(0, 0);

                // Try to find top anchor and scroll to it
                const topAnchor = window.parent.document.getElementById('top-anchor');
                if (topAnchor) {
                    topAnchor.scrollIntoView({ behavior: 'auto', block: 'start', inline: 'start' });
                }

                // Find and scroll all possible containers to top
                const selectors = [
                    '.main', '.stApp', 'section.main', '[data-testid="stAppViewContainer"]',
                    '[data-testid="block-container"]', '.css-1d391kg', '.css-18e3th9',
                    '.element-container', '.css-1lcbmhc', '.css-1outpf7', '.css-1y4p8pa',
                    '.css-12oz5g7', '.css-1rs6os', '.css-17eq0hr'
                ];

                selectors.forEach(selector => {
                    const elements = window.parent.document.querySelectorAll(selector);
                    elements.forEach(element => {
                        if (element) {
                            element.scrollTop = 0;
                            if (element.scrollTo) {
                                element.scrollTo(0, 0);
                                element.scrollTo({ top: 0, behavior: 'auto' });
                            }
                        }
                    });
                });

                // Additional force scroll methods
                if (window.parent.document.scrollingElement) {
                    window.parent.document.scrollingElement.scrollTop = 0;
                }

                // Try iframe scrolling if in iframe
                try {
                    window.scrollTo(0, 0);
                    document.body.scrollTop = 0;
                    document.documentElement.scrollTop = 0;
                } catch(e) {}

                // Force all elements with scroll to go to top
                const allElements = window.parent.document.querySelectorAll('*');
                allElements.forEach(el => {
                    if (el.scrollTop > 0) {
                        el.scrollTop = 0;
                    }
                });
            }

            // Execute immediately and with multiple delays for maximum effectiveness
            aggressiveScrollToTop();
            setTimeout(aggressiveScrollToTop, 1);
            setTimeout(aggressiveScrollToTop, 10);
            setTimeout(aggressiveScrollToTop, 50);
            setTimeout(aggressiveScrollToTop, 100);
            setTimeout(aggressiveScrollToTop, 200);
            setTimeout(aggressiveScrollToTop, 300);
            setTimeout(aggressiveScrollToTop, 500);
            setTimeout(aggressiveScrollToTop, 1000);
        </script>
        """, unsafe_allow_html=True)

        # Reset the flags
        st.session_state.scroll_to_top = False
        st.session_state.force_top_scroll = False

    # Display selected page
    pages[st.session_state.current_page]()

if __name__ == "__main__":
    main() 